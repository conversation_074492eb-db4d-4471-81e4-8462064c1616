"""
Main script for training MNIST Binary VAE
"""
import torch
import numpy as np
import random
import os

from config import Config
from data_loader import load_mnist_binary
from mnist_binary_vae import BinaryVAE
from training import train_model
from utils import create_directories


def set_random_seeds(seed):
    """Set random seeds for reproducibility"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def main():
    """Main training function"""
    # Load configuration
    config = Config()
    
    # Set random seeds
    set_random_seeds(config.random_seed)
    
    # Set device
    device = torch.device(config.device)
    print(f"Using device: {device}")
    
    # Create directories
    create_directories(config)
    
    # Load data
    print("Loading MNIST dataset...")
    train_loader, test_loader = load_mnist_binary(config)
    print(f"Training samples: {len(train_loader.dataset)}")
    print(f"Test samples: {len(test_loader.dataset)}")
    
    # Create model
    print("Creating Binary VAE model...")
    model = BinaryVAE(
        input_dim=config.input_dim,
        hidden_dim=config.hidden_dim,
        latent_dim=config.latent_dim
    ).to(device)
    
    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Print configuration
    print("\nTraining Configuration:")
    print(f"  Epochs: {config.epochs}")
    print(f"  Batch size: {config.batch_size}")
    print(f"  Learning rate: {config.learning_rate}")
    print(f"  Lambda KL: {config.lambda_kl}")
    print(f"  Dropout threshold: {config.dropout_threshold}")
    print(f"  Dropout start epoch: {config.dropout_start_epoch}")
    
    # Train model
    print("\nStarting training...")
    results = train_model(model, train_loader, test_loader, config, device)
    
    print("\nTraining completed!")
    print(f"Final training loss: {results['train_losses']['total'][-1]:.4f}")
    print(f"Final test loss: {results['test_losses']['total'][-1]:.4f}")
    print(f"Final active neurons: {results['train_active_counts'][-1]:.1f}")
    print(f"Final entropy: {results['train_entropies'][-1]:.4f}")
    
    # Save final results summary
    summary = {
        'config': {
            'epochs': config.epochs,
            'batch_size': config.batch_size,
            'learning_rate': config.learning_rate,
            'lambda_kl': config.lambda_kl,
            'dropout_threshold': config.dropout_threshold,
            'dropout_start_epoch': config.dropout_start_epoch,
            'random_seed': config.random_seed
        },
        'final_results': {
            'train_loss': results['train_losses']['total'][-1],
            'test_loss': results['test_losses']['total'][-1],
            'train_recon_loss': results['train_losses']['reconstruction'][-1],
            'test_recon_loss': results['test_losses']['reconstruction'][-1],
            'train_kl_loss': results['train_losses']['kl'][-1],
            'test_kl_loss': results['test_losses']['kl'][-1],
            'active_neurons': results['train_active_counts'][-1],
            'entropy': results['train_entropies'][-1]
        }
    }
    
    import json
    with open(os.path.join(config.results_path, 'training_summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\nResults saved to: {config.results_path}")
    print(f"Models saved to: {config.models_path}")


if __name__ == "__main__":
    main()
