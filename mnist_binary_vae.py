"""
MNIST Binary Variational Autoencoder with Reversible Dropout
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class StraightThrough<PERSON><PERSON><PERSON><PERSON>(torch.autograd.Function):
    """
    Straight-through estimator for <PERSON><PERSON><PERSON> sampling
    Forward: Hard sampling (0 or 1)
    Backward: Copy gradients as if identity function
    """
    @staticmethod
    def forward(ctx, probs):
        # Sample from Bernoulli distribution
        samples = torch.bernoulli(probs)
        return samples
    
    @staticmethod
    def backward(ctx, grad_output):
        # Straight-through: copy gradients
        return grad_output


class BinaryVAEEncoder(nn.Module):
    """
    Encoder network for Binary VAE
    """
    def __init__(self, input_dim, hidden_dim, latent_dim):
        super(BinaryVAEEncoder, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, latent_dim),
            nn.Sigmoid()  # Output Bernoulli parameters [0, 1]
        )
    
    def forward(self, x):
        """
        Encode input to Bernoulli parameters
        
        Args:
            x: Input data [batch_size, input_dim]
        
        Returns:
            q: <PERSON>oulli parameters [batch_size, latent_dim]
        """
        q = self.network(x)
        return q


class BinaryVAEDecoder(nn.Module):
    """
    Decoder network for Binary VAE
    """
    def __init__(self, latent_dim, hidden_dim, output_dim):
        super(BinaryVAEDecoder, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim),
            nn.Sigmoid()  # Output reconstruction probabilities
        )
    
    def forward(self, z):
        """
        Decode latent samples to reconstruction
        
        Args:
            z: Latent samples [batch_size, latent_dim]
        
        Returns:
            x_recon: Reconstruction probabilities [batch_size, output_dim]
        """
        x_recon = self.network(z)
        return x_recon


class BinaryVAE(nn.Module):
    """
    Complete Binary VAE with reversible dropout
    """
    def __init__(self, input_dim, hidden_dim, latent_dim):
        super(BinaryVAE, self).__init__()
        
        self.encoder = BinaryVAEEncoder(input_dim, hidden_dim, latent_dim)
        self.decoder = BinaryVAEDecoder(latent_dim, hidden_dim, input_dim)
        self.latent_dim = latent_dim
    
    def sample_latent(self, q):
        """
        Sample from Bernoulli distribution with straight-through estimator
        
        Args:
            q: Bernoulli parameters [batch_size, latent_dim]
        
        Returns:
            z: Binary samples [batch_size, latent_dim]
        """
        z = StraightThroughBernoulli.apply(q)
        return z
    
    def apply_reversible_dropout(self, q, z, threshold, apply_dropout=True):
        """
        Apply reversible dropout based on proximity to 0.5
        
        Args:
            q: Bernoulli parameters [batch_size, latent_dim]
            z: Sampled latent variables [batch_size, latent_dim]
            threshold: Dropout threshold L
            apply_dropout: Whether to apply dropout
        
        Returns:
            z_dropped: Latent variables with dropout applied
            active_mask: Boolean mask of active (non-dropped) variables
        """
        if not apply_dropout:
            active_mask = torch.ones_like(q, dtype=torch.bool)
            return z, active_mask
        
        # Check proximity to 0.5
        proximity_to_half = torch.abs(q - 0.5)
        active_mask = proximity_to_half >= threshold
        
        # Set dropped variables to 0.5
        z_dropped = torch.where(active_mask, z, 0.5)
        
        return z_dropped, active_mask

    def forward(self, x, threshold=0.05, apply_dropout=True):
        """
        Forward pass through the VAE

        Args:
            x: Input data [batch_size, input_dim]
            threshold: Dropout threshold
            apply_dropout: Whether to apply reversible dropout

        Returns:
            x_recon: Reconstructed data
            q: Bernoulli parameters
            z: Latent samples (after dropout)
            active_mask: Mask of active variables
        """
        # Encode
        q = self.encoder(x)

        # Sample
        z = self.sample_latent(q)

        # Apply reversible dropout
        z_dropped, active_mask = self.apply_reversible_dropout(
            q, z, threshold, apply_dropout
        )

        # Decode
        x_recon = self.decoder(z_dropped)

        return x_recon, q, z_dropped, active_mask

    def compute_kl_to_prior(self, q):
        """
        Compute KL divergence from Bernoulli(q) to Bernoulli(0.5)
        KL(Bern(q)||Bern(0.5)) = q*log(2q) + (1-q)*log(2(1-q))

        Args:
            q: Bernoulli parameters [batch_size, latent_dim]

        Returns:
            kl_loss: KL divergence loss
        """
        epsilon = 1e-8  # For numerical stability
        q = torch.clamp(q, epsilon, 1 - epsilon)

        kl_per_dim = q * torch.log(2 * q) + (1 - q) * torch.log(2 * (1 - q))
        kl_loss = torch.sum(kl_per_dim, dim=1)  # Sum over latent dimensions
        kl_loss = torch.mean(kl_loss)  # Average over batch

        return kl_loss
