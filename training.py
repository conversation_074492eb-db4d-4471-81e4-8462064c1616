"""
Training loop for MNIST Binary VAE
"""
import torch
import torch.optim as optim
from tqdm import tqdm
import numpy as np
from utils import (
    compute_reconstruction_loss, count_active_neurons, 
    compute_entropy_active_neurons, visualize_reconstructions,
    plot_training_curves, save_model
)


def train_epoch(model, train_loader, optimizer, config, epoch, device):
    """
    Train for one epoch
    
    Returns:
        Dictionary with average losses for the epoch
    """
    model.train()
    
    total_loss = 0
    total_recon_loss = 0
    total_kl_loss = 0
    total_active_count = 0
    total_entropy = 0
    num_batches = 0
    
    # Determine if dropout should be applied
    apply_dropout = epoch >= config.dropout_start_epoch
    
    progress_bar = tqdm(train_loader, desc=f'Epoch {epoch}')
    
    for batch_data, _ in progress_bar:
        batch_data = batch_data.to(device)
        batch_size = batch_data.shape[0]
        
        # Forward pass
        x_recon, q, z, active_mask = model(
            batch_data, 
            threshold=config.dropout_threshold,
            apply_dropout=apply_dropout
        )
        
        # Compute losses
        recon_loss = compute_reconstruction_loss(x_recon, batch_data)
        kl_loss = model.compute_kl_to_prior(q)
        
        # Total loss
        loss = recon_loss + config.lambda_kl * kl_loss
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()

        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

        optimizer.step()
        
        # Accumulate statistics
        total_loss += loss.item()
        total_recon_loss += recon_loss.item()
        total_kl_loss += kl_loss.item()
        
        # Count active neurons and compute entropy
        active_count = count_active_neurons(q, config.dropout_threshold)
        entropy = compute_entropy_active_neurons(q, active_mask)
        
        total_active_count += active_count
        total_entropy += entropy
        num_batches += 1
        
        # Update progress bar
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Recon': f'{recon_loss.item():.4f}',
            'KL': f'{kl_loss.item():.4f}',
            'Active': f'{active_count:.1f}'
        })
    
    # Return average losses
    return {
        'total': total_loss / num_batches,
        'reconstruction': total_recon_loss / num_batches,
        'kl': total_kl_loss / num_batches,
        'active_count': total_active_count / num_batches,
        'entropy': total_entropy / num_batches
    }


def evaluate_model(model, test_loader, config, epoch, device):
    """
    Evaluate model on test set
    """
    model.eval()
    
    total_loss = 0
    total_recon_loss = 0
    total_kl_loss = 0
    total_active_count = 0
    total_entropy = 0
    num_batches = 0
    
    apply_dropout = epoch >= config.dropout_start_epoch
    
    with torch.no_grad():
        for batch_data, _ in test_loader:
            batch_data = batch_data.to(device)
            
            # Forward pass
            x_recon, q, z, active_mask = model(
                batch_data,
                threshold=config.dropout_threshold,
                apply_dropout=apply_dropout
            )
            
            # Compute losses
            recon_loss = compute_reconstruction_loss(x_recon, batch_data)
            kl_loss = model.compute_kl_to_prior(q)
            loss = recon_loss + config.lambda_kl * kl_loss
            
            # Accumulate statistics
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()
            
            # Count active neurons and compute entropy
            active_count = count_active_neurons(q, config.dropout_threshold)
            entropy = compute_entropy_active_neurons(q, active_mask)
            
            total_active_count += active_count
            total_entropy += entropy
            num_batches += 1
    
    return {
        'total': total_loss / num_batches,
        'reconstruction': total_recon_loss / num_batches,
        'kl': total_kl_loss / num_batches,
        'active_count': total_active_count / num_batches,
        'entropy': total_entropy / num_batches
    }


def train_model(model, train_loader, test_loader, config, device):
    """
    Complete training loop
    """
    # Initialize optimizer
    optimizer = optim.Adam(model.parameters(), lr=config.learning_rate)
    
    # Initialize tracking lists
    train_losses = {'total': [], 'reconstruction': [], 'kl': []}
    test_losses = {'total': [], 'reconstruction': [], 'kl': []}
    train_active_counts = []
    train_entropies = []
    test_active_counts = []
    test_entropies = []
    
    print(f"Starting training for {config.epochs} epochs...")
    print(f"Dropout will start at epoch {config.dropout_start_epoch}")
    print(f"Dropout threshold: {config.dropout_threshold}")
    
    for epoch in range(1, config.epochs + 1):
        # Training
        train_stats = train_epoch(model, train_loader, optimizer, config, epoch, device)
        
        # Evaluation
        test_stats = evaluate_model(model, test_loader, config, epoch, device)
        
        # Store statistics
        train_losses['total'].append(train_stats['total'])
        train_losses['reconstruction'].append(train_stats['reconstruction'])
        train_losses['kl'].append(train_stats['kl'])
        train_active_counts.append(train_stats['active_count'])
        train_entropies.append(train_stats['entropy'])
        
        test_losses['total'].append(test_stats['total'])
        test_losses['reconstruction'].append(test_stats['reconstruction'])
        test_losses['kl'].append(test_stats['kl'])
        test_active_counts.append(test_stats['active_count'])
        test_entropies.append(test_stats['entropy'])
        
        # Print epoch summary
        print(f"Epoch {epoch:3d} | "
              f"Train Loss: {train_stats['total']:.4f} | "
              f"Test Loss: {test_stats['total']:.4f} | "
              f"Active Neurons: {train_stats['active_count']:.1f} | "
              f"Entropy: {train_stats['entropy']:.4f}")
        
        # Generate visualizations
        if epoch % config.plot_interval == 0:
            visualize_reconstructions(model, test_loader, config, epoch, device)
        
        # Save model
        if epoch % config.save_interval == 0:
            save_model(model, optimizer, epoch, {
                'train': train_losses,
                'test': test_losses,
                'train_active_counts': train_active_counts,
                'train_entropies': train_entropies,
                'test_active_counts': test_active_counts,
                'test_entropies': test_entropies
            }, config)
    
    # Final plots
    plot_training_curves(train_losses, train_active_counts, train_entropies, config)
    
    # Save final model
    save_model(model, optimizer, config.epochs, {
        'train': train_losses,
        'test': test_losses,
        'train_active_counts': train_active_counts,
        'train_entropies': train_entropies,
        'test_active_counts': test_active_counts,
        'test_entropies': test_entropies
    }, config)
    
    return {
        'train_losses': train_losses,
        'test_losses': test_losses,
        'train_active_counts': train_active_counts,
        'train_entropies': train_entropies,
        'test_active_counts': test_active_counts,
        'test_entropies': test_entropies
    }
