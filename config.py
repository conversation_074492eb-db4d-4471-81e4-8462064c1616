"""
Configuration file for MNIST Binary VAE with Total Correlation and Reversible Dropout
"""
import torch

class Config:
    # Model architecture
    input_dim = 784  # 28x28 MNIST images
    hidden_dim = 784  # Same as input/output for latent layer
    latent_dim = 784  # Latent dimension
    
    # Training hyperparameters
    batch_size = 128
    learning_rate = 5e-4
    epochs = 15
    
    # Loss function weights
    lambda_kl = 0.1  # Scaling factor for KL divergence to prior

    # Reversible dropout parameters
    dropout_threshold = 0.05  # L: threshold for dropout (proximity to 0.5)
    dropout_start_epoch = 5  # K: epochs to wait before starting dropout
    
    # Reporting and saving
    save_interval = 10  # Save model every N epochs
    plot_interval = 5   # Generate plots every N epochs
    num_reconstruction_samples = 10  # Number of reconstruction examples to show
    
    # Random seed for reproducibility
    random_seed = 42
    
    # Device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Paths
    data_path = './MNIST'
    results_path = './results'
    models_path = './models'
