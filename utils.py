"""
Utility functions for reporting and visualization
"""
import torch
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.gridspec import GridSpec


def create_directories(config):
    """Create necessary directories for saving results"""
    os.makedirs(config.results_path, exist_ok=True)
    os.makedirs(config.models_path, exist_ok=True)
    os.makedirs(os.path.join(config.results_path, 'reconstructions'), exist_ok=True)
    os.makedirs(os.path.join(config.results_path, 'plots'), exist_ok=True)


def compute_reconstruction_loss(x_recon, x_target):
    """
    Compute binary cross-entropy reconstruction loss
    
    Args:
        x_recon: Reconstructed data [batch_size, input_dim]
        x_target: Target data (binary -1/1) [batch_size, input_dim]
    
    Returns:
        recon_loss: Reconstruction loss
    """
    # Convert target from -1/1 to 0/1 for BCE
    x_target_01 = (x_target + 1) / 2
    
    # Clamp reconstruction for numerical stability
    epsilon = 1e-8
    x_recon = torch.clamp(x_recon, epsilon, 1 - epsilon)
    
    # Binary cross-entropy
    recon_loss = -torch.sum(
        x_target_01 * torch.log(x_recon) + (1 - x_target_01) * torch.log(1 - x_recon),
        dim=1
    )
    return torch.mean(recon_loss)


def count_active_neurons(q, threshold):
    """
    Count number of active neurons (far from 0.5)
    
    Args:
        q: Bernoulli parameters [batch_size, latent_dim]
        threshold: Dropout threshold
    
    Returns:
        active_count: Average number of active neurons per sample
    """
    proximity_to_half = torch.abs(q - 0.5)
    active_mask = proximity_to_half >= threshold
    active_count = torch.sum(active_mask, dim=1).float()
    return torch.mean(active_count).item()


def compute_entropy_active_neurons(q, active_mask):
    """
    Compute average entropy of active neurons
    
    Args:
        q: Bernoulli parameters [batch_size, latent_dim]
        active_mask: Boolean mask of active neurons
    
    Returns:
        avg_entropy: Average entropy of active neurons
    """
    epsilon = 1e-8
    q = torch.clamp(q, epsilon, 1 - epsilon)
    
    # Compute entropy: -q*log(q) - (1-q)*log(1-q)
    entropy = -q * torch.log(q) - (1 - q) * torch.log(1 - q)
    
    # Only consider active neurons
    active_entropy = entropy * active_mask.float()
    
    # Average over active neurons
    num_active = torch.sum(active_mask, dim=1).float()
    num_active = torch.clamp(num_active, min=1)  # Avoid division by zero
    
    avg_entropy_per_sample = torch.sum(active_entropy, dim=1) / num_active
    avg_entropy = torch.mean(avg_entropy_per_sample).item()
    
    return avg_entropy


def visualize_reconstructions(model, data_loader, config, epoch, device):
    """
    Visualize original vs reconstructed images
    """
    model.eval()
    with torch.no_grad():
        # Get a batch of data
        for batch_data, _ in data_loader:
            batch_data = batch_data.to(device)
            break
        
        # Get reconstructions
        x_recon, q, z, active_mask = model(
            batch_data[:config.num_reconstruction_samples],
            threshold=config.dropout_threshold,
            apply_dropout=(epoch >= config.dropout_start_epoch)
        )
        
        # Convert to numpy for plotting
        original = batch_data[:config.num_reconstruction_samples].cpu().numpy()
        reconstructed = x_recon.cpu().numpy()
        
        # Convert from -1/1 to 0/1 for visualization
        original = (original + 1) / 2
        
        # Create figure
        fig, axes = plt.subplots(2, config.num_reconstruction_samples, 
                                figsize=(config.num_reconstruction_samples * 2, 4))
        
        for i in range(config.num_reconstruction_samples):
            # Original
            axes[0, i].imshow(original[i].reshape(28, 28), cmap='gray')
            axes[0, i].set_title('Original')
            axes[0, i].axis('off')
            
            # Reconstructed
            axes[1, i].imshow(reconstructed[i].reshape(28, 28), cmap='gray')
            axes[1, i].set_title('Reconstructed')
            axes[1, i].axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(config.results_path, 'reconstructions', 
                                f'epoch_{epoch:03d}.png'), dpi=150, bbox_inches='tight')
        plt.close()


def plot_training_curves(losses, active_counts, entropies, config):
    """
    Plot training curves
    """
    epochs = range(1, len(losses['total']) + 1)
    
    fig = plt.figure(figsize=(15, 10))
    gs = GridSpec(2, 3, figure=fig)
    
    # Loss curves
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.plot(epochs, losses['total'], label='Total Loss')
    ax1.plot(epochs, losses['reconstruction'], label='Reconstruction')
    ax1.plot(epochs, losses['kl'], label='KL Divergence')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training Losses')
    ax1.legend()
    ax1.grid(True)
    
    # Active neurons count
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.plot(epochs, active_counts)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Number of Active Neurons')
    ax2.set_title('Active Neurons Over Time')
    ax2.grid(True)
    
    # Average entropy
    ax3 = fig.add_subplot(gs[0, 2])
    ax3.plot(epochs, entropies)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Average Entropy')
    ax3.set_title('Entropy of Active Neurons')
    ax3.grid(True)
    
    # Rate-distortion curve
    ax4 = fig.add_subplot(gs[1, :])
    ax4.scatter(losses['kl'], losses['reconstruction'], alpha=0.6, c=epochs, cmap='viridis')
    ax4.set_xlabel('KL Divergence (Compression)')
    ax4.set_ylabel('Reconstruction Loss (Distortion)')
    ax4.set_title('Rate-Distortion Curve')
    ax4.grid(True)
    cbar = plt.colorbar(ax4.collections[0], ax=ax4)
    cbar.set_label('Epoch')
    
    plt.tight_layout()
    plt.savefig(os.path.join(config.results_path, 'plots', 'training_curves.png'), 
                dpi=150, bbox_inches='tight')
    plt.close()


def save_model(model, optimizer, epoch, losses, config):
    """Save model checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'losses': losses
    }
    
    torch.save(checkpoint, os.path.join(config.models_path, f'model_epoch_{epoch:03d}.pth'))
    torch.save(checkpoint, os.path.join(config.models_path, 'latest_model.pth'))
