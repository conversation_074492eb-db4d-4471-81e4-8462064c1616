"""
Analysis script for MNIST Binary VAE results
"""
import torch
import matplotlib.pyplot as plt
import numpy as np
import json
import os
from config import Config
from mnist_binary_vae import BinaryVAE
from data_loader import load_mnist_binary
from utils import visualize_reconstructions


def load_model_checkpoint(checkpoint_path, config, device):
    """Load trained model from checkpoint"""
    model = BinaryVAE(
        input_dim=config.input_dim,
        hidden_dim=config.hidden_dim,
        latent_dim=config.latent_dim
    ).to(device)
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    return model, checkpoint


def analyze_latent_space(model, data_loader, config, device, num_samples=1000):
    """Analyze the learned latent space"""
    model.eval()
    
    all_q = []
    all_z = []
    all_active_masks = []
    
    with torch.no_grad():
        samples_collected = 0
        for batch_data, _ in data_loader:
            if samples_collected >= num_samples:
                break
                
            batch_data = batch_data.to(device)
            batch_size = min(batch_data.shape[0], num_samples - samples_collected)
            batch_data = batch_data[:batch_size]
            
            x_recon, q, z, active_mask = model(
                batch_data,
                threshold=config.dropout_threshold,
                apply_dropout=True
            )
            
            all_q.append(q.cpu())
            all_z.append(z.cpu())
            all_active_masks.append(active_mask.cpu())
            
            samples_collected += batch_size
    
    all_q = torch.cat(all_q, dim=0)
    all_z = torch.cat(all_z, dim=0)
    all_active_masks = torch.cat(all_active_masks, dim=0)
    
    return all_q, all_z, all_active_masks


def plot_latent_analysis(q, z, active_masks, config):
    """Create comprehensive latent space analysis plots"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. Distribution of Bernoulli parameters
    axes[0, 0].hist(q.flatten().numpy(), bins=50, alpha=0.7, density=True)
    axes[0, 0].axvline(x=0.5, color='red', linestyle='--', label='Prior (0.5)')
    axes[0, 0].axvline(x=0.5-config.dropout_threshold, color='orange', linestyle='--', alpha=0.7, label='Dropout bounds')
    axes[0, 0].axvline(x=0.5+config.dropout_threshold, color='orange', linestyle='--', alpha=0.7)
    axes[0, 0].set_xlabel('Bernoulli Parameter q')
    axes[0, 0].set_ylabel('Density')
    axes[0, 0].set_title('Distribution of Bernoulli Parameters')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Active neurons per sample
    active_counts = torch.sum(active_masks, dim=1).numpy()
    axes[0, 1].hist(active_counts, bins=30, alpha=0.7)
    axes[0, 1].set_xlabel('Number of Active Neurons')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title(f'Active Neurons per Sample\n(Mean: {np.mean(active_counts):.1f})')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Entropy distribution for active neurons
    epsilon = 1e-8
    q_clamped = torch.clamp(q, epsilon, 1 - epsilon)
    entropy = -q_clamped * torch.log(q_clamped) - (1 - q_clamped) * torch.log(1 - q_clamped)
    active_entropy = entropy * active_masks.float()
    
    # Only consider non-zero entropies (active neurons)
    active_entropy_flat = active_entropy[active_entropy > 0].numpy()
    axes[0, 2].hist(active_entropy_flat, bins=30, alpha=0.7)
    axes[0, 2].set_xlabel('Entropy')
    axes[0, 2].set_ylabel('Frequency')
    axes[0, 2].set_title(f'Entropy of Active Neurons\n(Mean: {np.mean(active_entropy_flat):.3f})')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. Neuron usage frequency
    neuron_usage = torch.mean(active_masks.float(), dim=0).numpy()
    axes[1, 0].plot(neuron_usage)
    axes[1, 0].set_xlabel('Neuron Index')
    axes[1, 0].set_ylabel('Usage Frequency')
    axes[1, 0].set_title('Neuron Usage Frequency')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. Correlation between q values and activity
    q_mean = torch.mean(q, dim=0).numpy()
    axes[1, 1].scatter(q_mean, neuron_usage, alpha=0.6)
    axes[1, 1].set_xlabel('Mean Bernoulli Parameter')
    axes[1, 1].set_ylabel('Usage Frequency')
    axes[1, 1].set_title('Parameter vs Usage Correlation')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. Distance from 0.5 for active vs inactive neurons
    distance_from_half = torch.abs(q - 0.5)
    active_distances = distance_from_half[active_masks].numpy()
    inactive_distances = distance_from_half[~active_masks].numpy()
    
    axes[1, 2].hist(active_distances, bins=30, alpha=0.7, label='Active', density=True)
    axes[1, 2].hist(inactive_distances, bins=30, alpha=0.7, label='Inactive', density=True)
    axes[1, 2].axvline(x=config.dropout_threshold, color='red', linestyle='--', label='Threshold')
    axes[1, 2].set_xlabel('Distance from 0.5')
    axes[1, 2].set_ylabel('Density')
    axes[1, 2].set_title('Distance from 0.5: Active vs Inactive')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(config.results_path, 'plots', 'latent_analysis.png'), 
                dpi=150, bbox_inches='tight')
    plt.close()
    
    # Print summary statistics
    print("\n=== Latent Space Analysis ===")
    print(f"Total neurons: {q.shape[1]}")
    print(f"Average active neurons per sample: {np.mean(active_counts):.1f} ± {np.std(active_counts):.1f}")
    print(f"Percentage of neurons used: {np.mean(neuron_usage > 0) * 100:.1f}%")
    print(f"Average entropy of active neurons: {np.mean(active_entropy_flat):.3f}")
    print(f"Bernoulli parameters - Mean: {torch.mean(q).item():.3f}, Std: {torch.std(q).item():.3f}")


def main():
    """Main analysis function"""
    config = Config()
    device = torch.device(config.device)
    
    # Check if results exist
    if not os.path.exists(config.results_path):
        print("No results found. Please run training first.")
        return
    
    # Load the latest model
    model_path = os.path.join(config.models_path, 'latest_model.pth')
    if not os.path.exists(model_path):
        print("No trained model found. Please run training first.")
        return
    
    print("Loading trained model...")
    model, checkpoint = load_model_checkpoint(model_path, config, device)
    
    # Load data
    print("Loading data...")
    train_loader, test_loader = load_mnist_binary(config)
    
    # Analyze latent space
    print("Analyzing latent space...")
    q, z, active_masks = analyze_latent_space(model, test_loader, config, device)
    
    # Create analysis plots
    plot_latent_analysis(q, z, active_masks, config)
    
    # Generate additional reconstructions
    print("Generating final reconstructions...")
    visualize_reconstructions(model, test_loader, config, 999, device)
    
    # Load and print training summary
    summary_path = os.path.join(config.results_path, 'training_summary.json')
    if os.path.exists(summary_path):
        with open(summary_path, 'r') as f:
            summary = json.load(f)
        
        print("\n=== Training Summary ===")
        print(f"Final training loss: {summary['final_results']['train_loss']:.4f}")
        print(f"Final test loss: {summary['final_results']['test_loss']:.4f}")
        print(f"Final reconstruction loss: {summary['final_results']['train_recon_loss']:.4f}")
        print(f"Final KL loss: {summary['final_results']['train_kl_loss']:.4f}")
    
    print(f"\nAnalysis complete! Results saved to: {config.results_path}")


if __name__ == "__main__":
    main()
