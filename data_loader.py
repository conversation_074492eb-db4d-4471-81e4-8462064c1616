"""
MNIST data loading and preprocessing for binary VAE
"""
import torch
import torchvision
import torchvision.transforms as transforms
import numpy as np
from torch.utils.data import DataLoader, Dataset


class BinaryMNISTDataset(Dataset):
    """
    Custom dataset that converts MNIST to binary format (-1/1)
    """
    def __init__(self, data, targets):
        self.data = data
        self.targets = targets
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # Convert to binary: 0 -> -1, 1 -> 1
        sample = self.data[idx].float()
        # Threshold at 0.5 to make binary, then convert 0->-1
        binary_sample = torch.where(sample > 0.5, 1.0, -1.0)
        return binary_sample.flatten(), self.targets[idx]


def load_mnist_binary(config):
    """
    Load MNIST dataset and convert to binary format
    
    Args:
        config: Configuration object with data_path and batch_size
    
    Returns:
        train_loader, test_loader: DataLoaders for training and testing
    """
    # Load MNIST dataset
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    
    train_dataset = torchvision.datasets.MNIST(
        root=config.data_path, 
        train=True, 
        download=False,  # Assuming data is already downloaded
        transform=transform
    )
    
    test_dataset = torchvision.datasets.MNIST(
        root=config.data_path, 
        train=False, 
        download=False,
        transform=transform
    )
    
    # Convert to binary format
    train_binary_dataset = BinaryMNISTDataset(
        train_dataset.data, 
        train_dataset.targets
    )
    
    test_binary_dataset = BinaryMNISTDataset(
        test_dataset.data, 
        test_dataset.targets
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_binary_dataset, 
        batch_size=config.batch_size, 
        shuffle=True,
        num_workers=2
    )
    
    test_loader = DataLoader(
        test_binary_dataset, 
        batch_size=config.batch_size, 
        shuffle=False,
        num_workers=2
    )
    
    return train_loader, test_loader


def get_sample_batch(data_loader, device):
    """
    Get a sample batch for visualization
    """
    for batch_data, batch_labels in data_loader:
        return batch_data.to(device), batch_labels.to(device)
