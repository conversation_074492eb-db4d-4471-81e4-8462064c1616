"""
MNIST data loading and preprocessing for binary VAE
"""
import torch
import torchvision
import torchvision.transforms as transforms
import numpy as np
import struct
import gzip
import os
from torch.utils.data import DataLoader, Dataset


class BinaryMNISTDataset(Dataset):
    """
    Custom dataset that converts MNIST to binary format (-1/1)
    """
    def __init__(self, data, targets):
        self.data = data
        self.targets = targets
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # Convert to binary: 0 -> -1, 1 -> 1
        sample = self.data[idx].float()
        # Threshold at 0.5 to make binary, then convert 0->-1
        binary_sample = torch.where(sample > 0.5, 1.0, -1.0)
        return binary_sample.flatten(), self.targets[idx]


def read_mnist_images(filename):
    """Read MNIST image file"""
    with open(filename, 'rb') as f:
        magic, num_images, rows, cols = struct.unpack('>IIII', f.read(16))
        images = np.frombuffer(f.read(), dtype=np.uint8)
        images = images.reshape(num_images, rows, cols)
    return images


def read_mnist_labels(filename):
    """Read MNIST label file"""
    with open(filename, 'rb') as f:
        magic, num_labels = struct.unpack('>II', f.read(8))
        labels = np.frombuffer(f.read(), dtype=np.uint8)
    return labels


def load_mnist_binary(config):
    """
    Load MNIST dataset and convert to binary format

    Args:
        config: Configuration object with data_path and batch_size

    Returns:
        train_loader, test_loader: DataLoaders for training and testing
    """
    try:
        # Try standard torchvision loading first
        transform = transforms.Compose([transforms.ToTensor()])

        train_dataset = torchvision.datasets.MNIST(
            root=config.data_path,
            train=True,
            download=False,
            transform=transform
        )

        test_dataset = torchvision.datasets.MNIST(
            root=config.data_path,
            train=False,
            download=False,
            transform=transform
        )

        print("Loaded MNIST using torchvision")

    except RuntimeError:
        # Load manually from the existing files
        print("Loading MNIST manually from existing files...")

        # Define file paths
        train_images_path = os.path.join(config.data_path, 'train-images.idx3-ubyte')
        train_labels_path = os.path.join(config.data_path, 'train-labels.idx1-ubyte')
        test_images_path = os.path.join(config.data_path, 't10k-images.idx3-ubyte')
        test_labels_path = os.path.join(config.data_path, 't10k-labels.idx1-ubyte')

        # Check if files exist
        if not all(os.path.exists(path) for path in [train_images_path, train_labels_path, test_images_path, test_labels_path]):
            raise FileNotFoundError("MNIST data files not found in the expected format")

        # Load data
        train_images = read_mnist_images(train_images_path)
        train_labels = read_mnist_labels(train_labels_path)
        test_images = read_mnist_images(test_images_path)
        test_labels = read_mnist_labels(test_labels_path)

        # Convert to tensors and normalize to [0, 1]
        train_images = torch.from_numpy(train_images).float() / 255.0
        train_labels = torch.from_numpy(train_labels).long()
        test_images = torch.from_numpy(test_images).float() / 255.0
        test_labels = torch.from_numpy(test_labels).long()

        # Create simple dataset classes
        class SimpleDataset:
            def __init__(self, images, labels):
                self.data = images
                self.targets = labels

        train_dataset = SimpleDataset(train_images, train_labels)
        test_dataset = SimpleDataset(test_images, test_labels)
    
    # Convert to binary format
    train_binary_dataset = BinaryMNISTDataset(
        train_dataset.data, 
        train_dataset.targets
    )
    
    test_binary_dataset = BinaryMNISTDataset(
        test_dataset.data, 
        test_dataset.targets
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_binary_dataset, 
        batch_size=config.batch_size, 
        shuffle=True,
        num_workers=2
    )
    
    test_loader = DataLoader(
        test_binary_dataset, 
        batch_size=config.batch_size, 
        shuffle=False,
        num_workers=2
    )
    
    return train_loader, test_loader


def get_sample_batch(data_loader, device):
    """
    Get a sample batch for visualization
    """
    for batch_data, batch_labels in data_loader:
        return batch_data.to(device), batch_labels.to(device)
