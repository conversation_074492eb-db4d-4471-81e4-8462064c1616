"""
Discriminator for Total Correlation estimation in Binary VAE
"""
import torch
import torch.nn as nn
import torch.nn.functional as F


class TCDiscriminator(nn.Module):
    """
    Discriminator network for estimating Total Correlation
    Distinguishes between joint samples and product of marginals
    """
    def __init__(self, latent_dim, hidden_dim=512):
        super(TCDiscriminator, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.<PERSON><PERSON><PERSON><PERSON>()
        )
    
    def forward(self, z):
        """
        Forward pass
        
        Args:
            z: Latent samples [batch_size, latent_dim]
        
        Returns:
            Probability that z comes from joint distribution
        """
        return self.network(z)


def create_permuted_samples(z):
    """
    Create product of marginals by permuting each dimension independently
    
    Args:
        z: Joint samples [batch_size, latent_dim]
    
    Returns:
        z_permuted: Permuted samples [batch_size, latent_dim]
    """
    batch_size, latent_dim = z.shape
    z_permuted = torch.zeros_like(z)
    
    for i in range(latent_dim):
        # Randomly permute the i-th dimension across the batch
        perm_idx = torch.randperm(batch_size)
        z_permuted[:, i] = z[perm_idx, i]
    
    return z_permuted


def estimate_total_correlation(discriminator, z_joint):
    """
    Estimate Total Correlation using the discriminator
    
    Args:
        discriminator: Trained discriminator network
        z_joint: Joint samples from encoder [batch_size, latent_dim]
    
    Returns:
        tc_estimate: Estimated total correlation
    """
    with torch.no_grad():
        # Get discriminator output for joint samples
        d_joint = discriminator(z_joint)
        
        # Estimate TC using the discriminator ratio
        # TC ≈ E[log(D(z) / (1 - D(z)))]
        epsilon = 1e-8  # For numerical stability
        d_joint = torch.clamp(d_joint, epsilon, 1 - epsilon)
        
        tc_estimate = torch.mean(torch.log(d_joint / (1 - d_joint)))
        
    return tc_estimate


def train_discriminator_step(discriminator, optimizer, z_joint):
    """
    Single training step for the discriminator
    
    Args:
        discriminator: Discriminator network
        optimizer: Discriminator optimizer
        z_joint: Joint samples from encoder
    
    Returns:
        discriminator_loss: Loss value
    """
    batch_size = z_joint.shape[0]
    
    # Create permuted samples (product of marginals)
    z_permuted = create_permuted_samples(z_joint.detach())
    
    # Labels: 1 for joint, 0 for permuted
    joint_labels = torch.ones(batch_size, 1, device=z_joint.device)
    permuted_labels = torch.zeros(batch_size, 1, device=z_joint.device)
    
    # Forward pass
    d_joint = discriminator(z_joint.detach())
    d_permuted = discriminator(z_permuted)
    
    # Binary cross-entropy loss
    loss_joint = F.binary_cross_entropy(d_joint, joint_labels)
    loss_permuted = F.binary_cross_entropy(d_permuted, permuted_labels)
    total_loss = loss_joint + loss_permuted
    
    # Backward pass
    optimizer.zero_grad()
    total_loss.backward()
    optimizer.step()
    
    return total_loss.item()
